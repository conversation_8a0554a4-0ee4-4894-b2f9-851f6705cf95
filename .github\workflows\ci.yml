name: 🚀 XR Gallery CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  GO_VERSION: '1.21'
  NODE_VERSION: '18'
  RUST_VERSION: 'stable'

jobs:
  # 🧪 Backend Go 测试和构建
  backend-test:
    name: 🔧 Backend Tests & Build
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐹 Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        
    - name: 📦 Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('backend/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
          
    - name: 📥 Download dependencies
      working-directory: ./backend
      run: go mod download
      
    - name: 🧪 Run tests
      working-directory: ./backend
      run: go test -v ./...
      
    - name: 🔍 Run go vet
      working-directory: ./backend
      run: go vet ./...
      
    - name: 🏗️ Build backend
      working-directory: ./backend
      run: go build -v ./cmd/main.go

  # 🦀 Rust 测试和构建
  rust-test:
    name: 🦀 Rust Tests & Build
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🦀 Setup Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: ${{ env.RUST_VERSION }}
        override: true
        components: rustfmt, clippy
        
    - name: 📦 Cache Rust dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          xr-rust/target
        key: ${{ runner.os }}-cargo-${{ hashFiles('xr-rust/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-
          
    - name: 🧪 Run tests
      working-directory: ./xr-rust
      run: cargo test --verbose
      
    - name: 🔍 Run clippy
      working-directory: ./xr-rust
      run: cargo clippy -- -D warnings
      
    - name: 🎨 Check formatting
      working-directory: ./xr-rust
      run: cargo fmt -- --check
      
    - name: 🏗️ Build release
      working-directory: ./xr-rust
      run: cargo build --release

  # 🎨 Frontend 测试和构建
  frontend-test:
    name: 🎨 Frontend Tests & Build
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: 📦 Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: latest
        
    - name: 📦 Cache pnpm dependencies
      uses: actions/cache@v3
      with:
        path: ~/.pnpm-store
        key: ${{ runner.os }}-pnpm-${{ hashFiles('frontend/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-
          
    - name: 📥 Install dependencies
      working-directory: ./frontend
      run: pnpm install --frozen-lockfile
      
    - name: 🧪 Run tests
      working-directory: ./frontend
      run: pnpm test
      
    - name: 🔍 Run linting
      working-directory: ./frontend
      run: pnpm lint
      
    - name: 🏗️ Build frontend
      working-directory: ./frontend
      run: pnpm build

  # 🚀 多平台构建
  multi-platform-build:
    name: 🚀 Multi-Platform Build
    runs-on: ubuntu-latest
    needs: [backend-test, rust-test, frontend-test]
    if: github.ref == 'refs/heads/main'
    
    strategy:
      matrix:
        include:
          - os: linux
            arch: amd64
          - os: linux
            arch: arm64
          - os: windows
            arch: amd64
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐹 Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        
    - name: 🏗️ Build backend for ${{ matrix.os }}-${{ matrix.arch }}
      working-directory: ./backend
      env:
        GOOS: ${{ matrix.os }}
        GOARCH: ${{ matrix.arch }}
        CGO_ENABLED: 0
      run: |
        if [ "${{ matrix.os }}" = "windows" ]; then
          go build -ldflags="-s -w" -o dist/xr-gallery-${{ matrix.os }}-${{ matrix.arch }}.exe ./cmd/main.go
        else
          go build -ldflags="-s -w" -o dist/xr-gallery-${{ matrix.os }}-${{ matrix.arch }} ./cmd/main.go
        fi
        
    - name: 📦 Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: xr-gallery-${{ matrix.os }}-${{ matrix.arch }}
        path: backend/dist/

  # 🐳 Docker 构建
  docker-build:
    name: 🐳 Docker Build
    runs-on: ubuntu-latest
    needs: [backend-test, rust-test, frontend-test]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐳 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 🔑 Login to Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        
    - name: 🏗️ Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          xrgallery/backend:latest
          xrgallery/backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 📊 代码质量检查
  code-quality:
    name: 📊 Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: 🔍 SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # 🎉 部署通知
  deploy-notification:
    name: 🎉 Deploy Notification
    runs-on: ubuntu-latest
    needs: [multi-platform-build, docker-build]
    if: github.ref == 'refs/heads/main' && success()
    
    steps:
    - name: 📢 Send success notification
      run: |
        echo "🎉 XR Gallery 构建成功！"
        echo "✅ Backend Go 构建完成"
        echo "✅ Rust 组件构建完成" 
        echo "✅ Frontend 构建完成"
        echo "✅ Docker 镜像构建完成"
        echo "🚀 准备部署到生产环境"
