package service

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
)

// 🌐 URLManager URL管理服务 - 与Rust版本完全一致的高度优雅实现
//
// 功能特性：
// - 🔄 智能域名检查和更新
// - 🛡️ 不跟随重定向策略，手动处理重定向
// - ⏱️ 15秒超时配置，与xr-rust版本完全一致
// - 📁 动态URL文件管理
// - 🎯 精确的重定向检测和处理
type URLManager struct {
	client       *http.Client
	proxyPrefix  string
	finalUrlFile string
}

// 🚀 NewURLManager 创建URL管理器实例 - 与xr-rust版本配置完全一致
//
// 配置特性：
// - ⏱️ 15秒超时：与xr-rust的Duration::from_secs(15)完全一致
// - 🚫 不跟随重定向：与xr-rust的Policy::none()策略一致
// - 📁 统一文件路径：finalUrl.txt文件管理
func NewURLManager() *URLManager {
	return &URLManager{
		client: &http.Client{
			// ⏱️ 15秒超时 - 与xr-rust版本的std::time::Duration::from_secs(15)完全一致
			Timeout: 15 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// 🚫 不跟随重定向策略 - 与xr-rust版本的redirect::Policy::none()一致
				// 我们需要手动处理重定向以检测域名变更
				return http.ErrUseLastResponse
			},
		},
		proxyPrefix:  "https://re.101616.xyz/",
		finalUrlFile: "finalUrl.txt", // 📁 与xr-rust版本使用相同的文件名
	}
}

// GetBaseURL 获取基础URL
func (u *URLManager) GetBaseURL() (string, error) {
	// 尝试从文件读取
	if content, err := os.ReadFile(u.finalUrlFile); err == nil {
		url := strings.TrimSpace(string(content))
		if url != "" {
			log.Printf("📁 从 %s 读取基础URL: %s", u.finalUrlFile, url)
			return url, nil
		}
	}

	// 使用默认URL
	defaultURL := "www.xiu01.top"
	log.Printf("⚠️  使用默认基础URL: %s", defaultURL)

	// 创建默认文件
	if err := u.updateFinalURL(defaultURL); err != nil {
		log.Printf("❌ 创建默认URL文件失败: %v", err)
	}

	return defaultURL, nil
}

// BuildProxyURL 构建代理URL
func (u *URLManager) BuildProxyURL(path string) (string, error) {
	baseURL, err := u.GetBaseURL()
	if err != nil {
		return "", err
	}

	// 清理baseURL，移除协议前缀
	cleanBaseURL := strings.TrimPrefix(baseURL, "http://")
	cleanBaseURL = strings.TrimPrefix(cleanBaseURL, "https://")
	cleanBaseURL = strings.TrimSuffix(cleanBaseURL, "/")

	// 构建完整URL
	fullURL := fmt.Sprintf("%s%s/%s", u.proxyPrefix, cleanBaseURL, path)
	return fullURL, nil
}

// 🔄 CheckAndUpdateBaseURL 检查并更新基础URL - 与xr-rust版本逻辑完全一致
//
// 执行流程：
// 1. 📁 读取finalUrl.txt文件获取当前URL
// 2. 🌐 构建测试URL（添加https前缀）
// 3. 🚀 发送HTTP请求检查状态（15秒超时）
// 4. 🔍 检测重定向并更新URL文件
// 5. ✅ 记录检查结果和状态变更
func (u *URLManager) CheckAndUpdateBaseURL() error {
	log.Printf("🔄 开始检查域名状态")

	// 📁 获取当前基础URL - 与xr-rust版本的文件读取逻辑一致
	baseURL, err := u.GetBaseURL()
	if err != nil {
		return fmt.Errorf("🚨 获取基础URL失败: %w", err)
	}

	// 🌐 构建测试URL - 与xr-rust版本的URL构建逻辑一致
	testURL := baseURL
	if !strings.HasPrefix(testURL, "http") {
		testURL = "https://" + testURL
	}

	log.Printf("🌐 检查URL: %s", testURL)

	// 🚀 发送HTTP请求 - 使用15秒超时和不跟随重定向策略
	// 与xr-rust版本的client.get(&test_url).send().await完全对应
	resp, err := u.client.Get(testURL)
	if err != nil {
		log.Printf("⚠️  域名检查失败: %s - %v", baseURL, err)
		return fmt.Errorf("🚨 域名检查失败: %w", err)
	}
	defer resp.Body.Close()

	status := resp.StatusCode
	log.Printf("📊 HTTP状态码: %d", status)

	// 🔍 检查重定向状态 - 与xr-rust版本的status.is_redirection()逻辑一致
	if status >= 300 && status < 400 {
		location := resp.Header.Get("Location")
		if location != "" && location != baseURL {
			log.Printf("🔄 发现重定向: %s -> %s", baseURL, location)

			// 🧹 清理新URL - 与xr-rust版本的URL处理逻辑保持一致
			// 移除协议前缀和尾部斜杠，确保URL格式统一
			newURL := strings.TrimPrefix(location, "http://")
			newURL = strings.TrimPrefix(newURL, "https://")
			newURL = strings.TrimSuffix(newURL, "/")

			if newURL != baseURL {
				// 📝 更新文件中的URL - 对应xr-rust版本的tokio::fs::write操作
				if err := u.updateFinalURL(newURL); err != nil {
					log.Printf("❌ 更新URL文件失败: %v", err)
					return fmt.Errorf("🚨 更新URL文件失败: %w", err)
				}
				log.Printf("🎉 域名已更新: %s -> %s", baseURL, newURL)
			} else {
				log.Printf("✅ 域名无变化: %s", baseURL)
			}
		}
	} else if status >= 200 && status < 300 {
		// ✅ 成功状态 - 与xr-rust版本的status.is_success()对应
		log.Printf("✅ 域名检查成功: %s", baseURL)
	} else {
		// ⚠️ 异常状态 - 与xr-rust版本的异常处理逻辑一致
		log.Printf("⚠️  域名响应异常: %s - HTTP %d", baseURL, status)
		return fmt.Errorf("🚨 域名响应异常: HTTP %d", status)
	}

	return nil
}

// updateFinalURL 更新finalUrl.txt文件
func (u *URLManager) updateFinalURL(newURL string) error {
	// 确保URL格式正确
	cleanURL := strings.TrimSpace(newURL)
	cleanURL = strings.TrimPrefix(cleanURL, "http://")
	cleanURL = strings.TrimPrefix(cleanURL, "https://")
	cleanURL = strings.TrimSuffix(cleanURL, "/")

	// 写入文件
	err := os.WriteFile(u.finalUrlFile, []byte(cleanURL), 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	log.Printf("📝 已更新 %s: %s", u.finalUrlFile, cleanURL)
	return nil
}

// GetCurrentURL 获取当前使用的完整URL（用于调试）
func (u *URLManager) GetCurrentURL() (string, error) {
	return u.BuildProxyURL("XiuRen/")
}

// TestConnection 测试连接
func (u *URLManager) TestConnection() error {
	testURL, err := u.GetCurrentURL()
	if err != nil {
		return fmt.Errorf("构建测试URL失败: %w", err)
	}

	log.Printf("🧪 测试连接: %s", testURL)

	req, err := http.NewRequest("GET", testURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP请求失败: %d", resp.StatusCode)
	}

	// 读取少量内容验证
	body, err := io.ReadAll(io.LimitReader(resp.Body, 1024))
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	log.Printf("✅ 连接测试成功，响应长度: %d 字节", len(body))
	return nil
}
