package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"xr-gallery/internal/config"
	"xr-gallery/internal/database"
	"xr-gallery/internal/handler"
	"xr-gallery/internal/middleware"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/app/server"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig(config.GetConfigPath())
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	if err := database.InitDB(cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// 初始化并启动调度器
	handler.InitScheduler(cfg.Server.Host, cfg.Server.Port)
	if err := handler.StartSchedulerOnBoot(); err != nil {
		log.Printf("⚠️  启动调度器失败: %v", err)
	} else {
		log.Printf("🚀 调度器已自动启动")
	}

	// 创建Hertz服务器实例
	addr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	h := server.Default(server.WithHostPorts(addr))

	// 添加CORS中间件
	h.Use(middleware.CORSMiddleware(cfg.CORS.AllowOrigins))

	// 注册图片代理路由
	h.GET("/api/img", handler.ImageProxyHandler)

	// 注册图库API路由
	h.GET("/api/gallery/list", handler.GetGalleryList)
	h.GET("/api/gallery/detail/:xrid", handler.GetGalleryDetail)

	// 注册爬虫API路由
	h.GET("/getlist", handler.GetList)
	h.GET("/reurl", handler.Reurl)
	h.GET("/refmurl", handler.Refmurl)
	h.GET("/getimglist", handler.GetImgList)
	h.GET("/upnull", handler.Upnull)
	h.GET("/retry", handler.Retry)

	// 注册删除API路由 - 支持GET和DELETE方法
	h.GET("/del/:xrid", handler.DeleteXRID)
	h.DELETE("/del/:xrid", handler.DeleteXRID)

	// 注册调度器API路由
	h.GET("/scheduler/start", handler.StartScheduler)
	h.GET("/scheduler/stop", handler.StopScheduler)
	h.GET("/scheduler/status", handler.GetSchedulerStatus)
	h.GET("/scheduler/interval", handler.SetSchedulerInterval)
	h.GET("/scheduler/maxpages", handler.SetSchedulerMaxPages)

	// 基础路由测试
	h.GET("/ping", func(ctx context.Context, c *app.RequestContext) {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"message":    "pong",
			"service":    "xr-gallery",
			"database":   "connected",
			"hot_reload": "enabled",
			"version":    "dev-1.0.0",
		})
	})

	// 设置优雅关闭
	setupGracefulShutdown()

	// 启动服务器
	log.Printf("XR Gallery server starting on %s", addr)
	h.Spin()
}

// setupGracefulShutdown 设置优雅关闭
func setupGracefulShutdown() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Printf("🛑 收到关闭信号，正在优雅关闭...")

		// 停止调度器
		handler.StopSchedulerOnShutdown()

		// 关闭数据库连接
		database.CloseDB()

		log.Printf("✅ 服务已优雅关闭")
		os.Exit(0)
	}()
}
