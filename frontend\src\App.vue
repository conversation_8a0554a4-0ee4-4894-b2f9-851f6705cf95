<template>
  <n-config-provider
    :theme="theme"
    :locale="locale"
    :date-locale="dateLocale"
    :theme-overrides="themeOverrides"
  >
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <n-message-provider>
            <div id="app">
              <router-view />
            </div>
          </n-message-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useOsTheme } from 'naive-ui'
import { zhCN, dateZhCN, enUS, dateEnUS } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'

// 认证状态管理
const authStore = useAuthStore()

// 主题配置
const osTheme = useOsTheme()
const theme = ref(null) // null表示跟随系统主题

// 国际化配置
const locale = ref(zhCN)
const dateLocale = ref(dateZhCN)

// 主题覆盖配置
const themeOverrides = computed(() => ({
  common: {
    primaryColor: '#18a058',
    primaryColorHover: '#36ad6a',
    primaryColorPressed: '#0c7a43',
    borderRadius: '6px'
  },
  Card: {
    borderRadius: '8px'
  },
  Button: {
    borderRadius: '6px'
  }
}))

// 响应式主题切换（可选功能）
function toggleTheme() {
  theme.value = theme.value === null ? 'dark' : null
}

// 切换语言（可选功能）
function toggleLocale() {
  if (locale.value === zhCN) {
    locale.value = enUS
    dateLocale.value = dateEnUS
  } else {
    locale.value = zhCN
    dateLocale.value = dateZhCN
  }
}

// 应用启动时初始化认证状态
onMounted(() => {
  // 从localStorage恢复认证状态
  authStore.checkAuth()
  console.log('应用启动，认证状态检查完成')
})

// 暴露给模板使用（如果需要）
defineExpose({
  toggleTheme,
  toggleLocale
})
</script>

<style>
/* 全局样式已移至 styles/main.css */
</style>
