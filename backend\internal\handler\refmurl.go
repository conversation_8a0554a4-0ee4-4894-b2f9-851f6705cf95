package handler

import (
	"context"
	"fmt"
	"log"
	"xr-gallery/internal/repository"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// 🎨 Refmurl 封面图片上传处理器 - 与xr-rust版本完全一致的高度优雅实现
//
// 功能特性：
// - 🎯 串行处理，每30秒处理一张封面图片，避免并发冲突
// - 🔄 智能重复检测，复用已上传的封面结果
// - 🛡️ 完整的错误处理和状态管理
// - 📊 详细的处理日志和统计信息
// - ⚡ 高性能Telegraph图片上传
func Refmurl(ctx context.Context, c *app.RequestContext) {
	log.Printf("🎨 开始处理封面图片上传请求 - 每30秒串行处理模式")

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 获取待处理封面记录
	record, err := repo.GetPendingCoverRecord()
	if err != nil {
		log.Printf("❌ 获取待处理封面记录失败: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"error":   fmt.Sprintf("获取待处理封面记录失败: %v", err),
		})
		return
	}

	if record == nil {
		log.Printf("📭 没有待处理的封面记录")
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success": false,
			"message": "no_records",
		})
		return
	}

	if record.FM == "" {
		log.Printf("❌ 封面URL为空: id=%d", record.ID)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(400, utils.H{
			"success": false,
			"error":   "封面URL为空",
			"id":      record.ID,
		})
		return
	}

	log.Printf("🎯 处理封面: id=%d, xrid=%d, fm=%s", record.ID, record.XRID, record.FM)

	// 检查是否已经处理过（防重复上传）
	existingRefm, err := repo.CheckExistingRefm(record.FM)
	if err != nil {
		log.Printf("⚠️  检查重复失败: %v", err)
		// 继续处理，不阻止上传
	} else if existingRefm != "" {
		log.Printf("♻️  发现重复封面，复用已上传结果: %s -> %s", record.FM, existingRefm)

		// 更新当前记录
		if err := repo.UpdateCoverRefm(record.ID, existingRefm); err != nil {
			log.Printf("⚠️  更新重复结果失败: %v", err)
		}

		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success": true,
			"reason":  "reused_existing",
			"id":      record.ID,
			"fm":      record.FM,
			"refm":    existingRefm,
		})
		return
	}

	// 标记为处理中
	if err := repo.UpdateCoverRefm(record.ID, "processing"); err != nil {
		log.Printf("⚠️  更新处理状态失败: %v", err)
	}

	// 创建上传服务并处理封面
	uploadService := NewImageUploadService()

	// 构建完整封面URL
	fullCoverURL, err := buildImageURL(record.FM)
	if err != nil {
		log.Printf("❌ 构建封面URL失败: %v", err)
		repo.UpdateCoverRefm(record.ID, "4040")
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"id":      record.ID,
			"error":   fmt.Sprintf("构建封面URL失败: %v", err),
		})
		return
	}

	log.Printf("🌐 完整封面URL: %s", fullCoverURL)

	// 上传封面
	result, err := uploadService.UploadImage(fullCoverURL)
	if err != nil {
		log.Printf("❌ 上传封面失败: id=%d, error=%v", record.ID, err)

		// 上传失败，标记为4040
		repo.UpdateCoverRefm(record.ID, "4040")

		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"id":      record.ID,
			"xrid":    record.XRID,
			"error":   fmt.Sprintf("上传封面失败: %v", err),
		})
		return
	}

	// 更新数据库
	if err := repo.UpdateCoverRefm(record.ID, result.Src); err != nil {
		log.Printf("❌ 更新refm失败: %v", err)
	}

	log.Printf("🎉 封面上传成功: id=%d, %s -> %s", record.ID, record.FM, result.Src)

	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"success": true,
		"id":      record.ID,
		"xrid":    record.XRID,
		"fm":      record.FM,
		"refm":    result.Src,
	})
}
