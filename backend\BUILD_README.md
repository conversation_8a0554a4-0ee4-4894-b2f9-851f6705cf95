# 🚀 XR Gallery Backend 多平台构建指南

## 📋 概述

基于 `build.yml` 配置文件的统一多平台构建方案，支持 Windows x64、Linux x64、Linux ARM64 三个平台的自动化编译。

## 🎯 快速开始

### 方法一：使用 Go 构建工具（推荐）

```bash
# 1. 确保已安装 Go 1.19+
go version

# 2. 安装 YAML 依赖
go mod tidy

# 3. 运行构建工具
go run builder.go
```

### 方法二：使用 PowerShell 脚本（兼容原方案）

```powershell
# Windows 环境下运行
.\build_and_run.ps1
```

## 📁 文件结构

```
backend/
├── build.yml          # 🔧 构建配置文件
├── builder.go          # 🏗️ Go 构建工具
├── build_and_run.ps1   # 📜 PowerShell 构建脚本
├── cmd/main.go         # 🎯 程序入口文件
└── dist/               # 📦 输出目录
    ├── main_win_amd64.exe      # Windows x64
    ├── main_linux_amd64        # Linux x64
    └── main_linux_arm64        # Linux ARM64
```

## ⚙️ 配置说明

### build.yml 主要配置项

```yaml
# 📦 项目信息
project:
  name: "xr-gallery-backend"
  version: "1.0.0"
  entry_file: "cmd/main.go"

# 📁 构建配置
build:
  output_dir: "dist"

# 🎯 目标平台
targets:
  - name: "windows-amd64"
    display_name: "Windows x64"
    goos: "windows"
    goarch: "amd64"
    output_name: "main_win_amd64.exe"
    enabled: true
    
  - name: "linux-amd64"
    display_name: "Linux x64"
    goos: "linux"
    goarch: "amd64"
    output_name: "main_linux_amd64"
    enabled: true
    
  - name: "linux-arm64"
    display_name: "Linux ARM64"
    goos: "linux"
    goarch: "arm64"
    output_name: "main_linux_arm64"
    enabled: true
```

## 🔧 自定义配置

### 启用/禁用特定平台

```yaml
targets:
  - name: "windows-amd64"
    enabled: false  # 禁用 Windows 构建
```

### 修改输出文件名

```yaml
targets:
  - name: "linux-amd64"
    output_name: "xr-gallery-linux-x64"  # 自定义文件名
```

### 调整项目版本

```yaml
project:
  version: "2.0.0"  # 版本号会注入到可执行文件中
```

## 🚀 构建输出示例

```
🚀 XR Gallery Backend 多平台构建工具
📋 基于 build.yml 配置文件的统一构建方案

📦 项目: xr-gallery-backend v1.0.0

📁 输出目录: dist
🏗️ 开始多平台构建...
🎯 构建 Windows x64...
✅ Windows x64 构建成功
🎯 构建 Linux x64...
✅ Linux x64 构建成功
🎯 构建 Linux ARM64...
✅ Linux ARM64 构建成功

📊 构建结果统计:
   ✅ 成功: 3/3
🎉 所有平台构建成功！

📁 输出文件:
   📄 main_win_amd64.exe (15.2 MB)
   📄 main_linux_amd64 (14.8 MB)
   📄 main_linux_arm64 (14.5 MB)

🎉 构建流程完成！
```

## 🔍 故障排除

### 常见问题

1. **Go 版本过低**
   ```bash
   # 确保 Go 版本 >= 1.19
   go version
   ```

2. **依赖缺失**
   ```bash
   # 安装 YAML 依赖
   go mod tidy
   ```

3. **权限问题**
   ```bash
   # Linux/macOS 设置执行权限
   chmod +x dist/main_linux_*
   ```

4. **配置文件错误**
   ```bash
   # 检查 YAML 语法
   go run builder.go
   ```

### 调试模式

如果构建失败，可以手动执行单个平台构建：

```bash
# Windows x64
GOOS=windows GOARCH=amd64 CGO_ENABLED=0 go build -o dist/main_win_amd64.exe cmd/main.go

# Linux x64
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o dist/main_linux_amd64 cmd/main.go

# Linux ARM64
GOOS=linux GOARCH=arm64 CGO_ENABLED=0 go build -o dist/main_linux_arm64 cmd/main.go
```

## 🎨 高级功能

### 版本信息注入

构建工具会自动注入版本信息：

```go
// 在 main.go 中可以使用
var (
    version   = "dev"
    buildTime = "unknown"
)

func main() {
    fmt.Printf("Version: %s\n", version)
    fmt.Printf("Build Time: %s\n", buildTime)
}
```

### 构建优化

- **-s -w**: 去除符号表和调试信息，减小文件大小
- **CGO_ENABLED=0**: 禁用 CGO，确保静态链接
- **-trimpath**: 去除文件路径信息

## 📊 性能对比

| 方案 | 构建时间 | 文件大小 | 易用性 | 可扩展性 |
|------|----------|----------|--------|----------|
| **YAML + Go** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **PowerShell** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |

## 🔄 CI/CD 集成

### GitHub Actions 示例

```yaml
name: Build
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.19'
      - name: Build
        run: |
          cd backend
          go mod tidy
          go run builder.go
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: binaries
          path: backend/dist/
```

## 📝 更新日志

- **v1.0.0**: 初始版本，支持三平台构建
- **v1.1.0**: 添加 YAML 配置支持
- **v1.2.0**: 增加版本信息注入
- **v1.3.0**: 优化构建输出和错误处理

---

**维护者**: XR Gallery Team  
**最后更新**: 2025-01-30
