package service

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"xr-gallery/internal/repository"

	"github.com/PuerkitoBio/goquery"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// CrawlerService 爬虫服务
type CrawlerService struct {
	client     *http.Client
	userAgent  string
	repo       *repository.GalleryRepository
	urlManager *URLManager
}

// NewCrawlerService 创建爬虫服务实例
func NewCrawlerService() *CrawlerService {
	return &CrawlerService{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		repo:       repository.NewGalleryRepository(),
		urlManager: NewURLManager(),
	}
}

// ListItem 列表项数据结构
type ListItem struct {
	XRID  int    `json:"xrid"`
	FM    string `json:"fm"`
	Title string `json:"title"`
	URL   string `json:"url"`
}

// PageLink 分页链接结构体 - 与xr-rust版本保持一致
type PageLink struct {
	Page int    `json:"page"`
	URL  string `json:"url"`
}

// GetListPage 获取列表页数据
func (s *CrawlerService) GetListPage(page int) ([]ListItem, error) {
	log.Printf("🎯 开始爬取第%d页列表数据", page)

	// 构建URL
	var path string
	if page == 1 {
		path = "XiuRen/"
	} else {
		path = fmt.Sprintf("XiuRen/index%d.html", page)
	}

	pageURL, err := s.urlManager.BuildProxyURL(path)
	if err != nil {
		return nil, fmt.Errorf("构建URL失败: %w", err)
	}

	log.Printf("📡 请求URL: %s", pageURL)

	// 创建请求
	req, err := http.NewRequest("GET", pageURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头 - 确保正确处理中文编码
	req.Header.Set("User-Agent", s.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Accept-Encoding", "identity") // 不使用压缩，避免解压问题
	req.Header.Set("Accept-Charset", "utf-8,gbk,gb2312;q=0.7,*;q=0.3")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	log.Printf("📊 HTML内容长度: %d 字节", len(body))

	// 处理中文编码问题
	htmlContent := string(body)

	// 检测并转换编码
	if !isValidUTF8(htmlContent) {
		log.Printf("🔄 检测到非UTF-8编码，尝试转换...")
		if convertedContent, err := convertToUTF8(body); err == nil {
			htmlContent = convertedContent
			log.Printf("✅ 编码转换成功")
		} else {
			log.Printf("⚠️  编码转换失败，使用原始内容: %v", err)
		}
	}

	// 解析HTML - 使用转换后的内容
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %w", err)
	}

	// 调试：检查页面结构
	iListCount := doc.Find(".i_list").Length()
	listN2Count := doc.Find(".list_n2").Length()
	combinedCount := doc.Find(".i_list.list_n2").Length()

	log.Printf("🔍 找到的 .i_list 元素数量: %d", iListCount)
	log.Printf("🔍 找到的 .list_n2 元素数量: %d", listN2Count)
	log.Printf("🔍 找到的 .i_list.list_n2 元素数量: %d", combinedCount)

	// 显示HTML内容预览
	htmlPreview := htmlContent
	if len(htmlPreview) > 500 {
		htmlPreview = htmlPreview[:500] + "..."
	}
	log.Printf("📄 HTML内容预览: %s", htmlPreview)

	// 提取列表项
	var items []ListItem
	processedCount := 0

	doc.Find(".i_list.list_n2").Each(func(i int, s *goquery.Selection) {
		processedCount++
		item, err := extractListItem(s, i+1)
		if err != nil {
			log.Printf("⚠️  提取第%d项失败: %v", i+1, err)
			return
		}
		if item != nil {
			log.Printf("✅ 提取成功 #%d: xrid=%d, title=%s", i+1, item.XRID, item.Title)
			items = append(items, *item)
		}
	})

	log.Printf("🎉 第%d页爬取完成: 处理%d个元素，提取%d条有效记录", page, processedCount, len(items))

	if len(items) == 0 && processedCount == 0 {
		log.Printf("⚠️  未找到任何 .i_list.list_n2 元素，可能页面结构已变化")
	}

	log.Printf("🎉 第%d页爬取完成: 提取%d条有效记录", page, len(items))

	return items, nil
}

// GetDetailImages 获取详情页图片列表
func (s *CrawlerService) GetDetailImages(detailURL string) ([]string, error) {
	log.Printf("🕷️ 开始爬取详情页图片: %s", detailURL)

	// 构建完整URL
	fullURL, err := s.urlManager.BuildProxyURL(detailURL)
	if err != nil {
		return nil, fmt.Errorf("构建完整URL失败: %w", err)
	}

	log.Printf("🌐 完整详情页URL: %s", fullURL)

	// 发送HTTP请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("User-Agent", s.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(body)))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %w", err)
	}

	var images []string

	// 🖼️ 使用与xr-rust版本完全一致的选择器和过滤逻辑
	// 精确选择器：div.content div.content_left img
	doc.Find("div.content div.content_left img").Each(func(i int, s *goquery.Selection) {
		// 🎯 优先获取data-original，备选src - 与xr-rust逻辑一致
		src, exists := s.Attr("data-original")
		if !exists {
			src, exists = s.Attr("src")
			if !exists {
				return
			}
		}

		// 🚫 过滤无效图片
		if src == "" || strings.Contains(src, "data:image") || strings.Contains(src, "base64") {
			return
		}

		// 🎯 关键过滤条件：与xr-rust版本完全一致
		// 只保留包含"uploadfile"的图片URL
		if strings.Contains(src, "uploadfile") {
			log.Printf("📸 发现图片: %s", src)
			images = append(images, src)
		}
	})

	// 🔍 如果精确选择器没有找到图片，尝试备用选择器
	if len(images) == 0 {
		log.Printf("🔄 精确选择器未找到图片，尝试备用选择器...")

		doc.Find("img").Each(func(i int, s *goquery.Selection) {
			// 优先获取data-original，备选src
			src, exists := s.Attr("data-original")
			if !exists {
				src, exists = s.Attr("src")
				if !exists {
					return
				}
			}

			// 过滤无效图片
			if src == "" || strings.Contains(src, "data:image") || strings.Contains(src, "base64") {
				return
			}

			// 过滤小图片和广告图片
			if strings.Contains(src, "logo") || strings.Contains(src, "icon") ||
				strings.Contains(src, "banner") || strings.Contains(src, "ad") {
				return
			}

			// 扩展的过滤条件：包含uploadfile或常见图片路径
			if strings.Contains(src, "uploadfile") ||
				strings.Contains(src, "/uploads/") ||
				strings.Contains(src, "/images/") ||
				strings.Contains(src, "/upload/") {
				log.Printf("📸 备用选择器发现图片: %s", src)
				images = append(images, src)
			}
		})
	}

	log.Printf("📸 第1页提取到%d张图片", len(images))

	// 🔗 提取分页链接 - 与xr-rust版本完全一致的分页处理
	pageLinks := s.extractPageLinks(doc)
	log.Printf("🔗 发现%d个分页链接", len(pageLinks))

	// 🔄 处理其他页面
	if len(pageLinks) > 0 {
		// 从fullURL中提取基础URL
		baseURL := strings.Split(fullURL, detailURL)[0]

		for _, pageLink := range pageLinks {
			log.Printf("🔄 处理第%d页: %s", pageLink.Page, pageLink.URL)

			// 🌐 构建完整URL
			pageURL := fmt.Sprintf("%s%s", baseURL, pageLink.URL)

			// 🚀 发起HTTP请求
			pageReq, err := http.NewRequest("GET", pageURL, nil)
			if err != nil {
				log.Printf("⚠️  第%d页请求创建失败: %v", pageLink.Page, err)
				continue
			}

			pageReq.Header.Set("User-Agent", s.userAgent)
			pageReq.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
			pageReq.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")

			pageResp, err := s.client.Do(pageReq)
			if err != nil {
				log.Printf("⚠️  第%d页请求失败: %v", pageLink.Page, err)
				continue
			}

			if pageResp.StatusCode != 200 {
				log.Printf("⚠️  第%d页HTTP状态异常: %d", pageLink.Page, pageResp.StatusCode)
				pageResp.Body.Close()
				continue
			}

			// 📖 读取页面内容
			pageBody, err := io.ReadAll(pageResp.Body)
			pageResp.Body.Close()
			if err != nil {
				log.Printf("⚠️  第%d页读取失败: %v", pageLink.Page, err)
				continue
			}

			// 🔤 处理编码
			pageContent := string(pageBody)
			if !isValidUTF8(pageContent) {
				if convertedContent, err := convertToUTF8(pageBody); err == nil {
					pageContent = convertedContent
				}
			}

			// 📄 解析HTML
			pageDoc, err := goquery.NewDocumentFromReader(strings.NewReader(pageContent))
			if err != nil {
				log.Printf("⚠️  第%d页HTML解析失败: %v", pageLink.Page, err)
				continue
			}

			// 🖼️ 提取该页图片
			var pageImages []string
			pageDoc.Find("div.content div.content_left img").Each(func(i int, s *goquery.Selection) {
				src, exists := s.Attr("data-original")
				if !exists {
					src, exists = s.Attr("src")
					if !exists {
						return
					}
				}

				if src != "" && strings.Contains(src, "uploadfile") {
					log.Printf("📸 第%d页图片: %s", pageLink.Page, src)
					pageImages = append(pageImages, src)
				}
			})

			images = append(images, pageImages...)
			log.Printf("📸 第%d页提取到%d张图片", pageLink.Page, len(pageImages))

			// ⏱️ 添加延迟避免请求过快 - 与xr-rust版本一致
			time.Sleep(500 * time.Millisecond)
		}
	}

	// 去重
	imageMap := make(map[string]bool)
	var uniqueImages []string
	for _, img := range images {
		if !imageMap[img] {
			imageMap[img] = true
			uniqueImages = append(uniqueImages, img)
		}
	}

	log.Printf("🎉 详情页爬取完成，共获取%d张图片", len(uniqueImages))
	return uniqueImages, nil
}

// extractListItem 提取单个列表项数据
func extractListItem(s *goquery.Selection, index int) (*ListItem, error) {
	// 查找图片元素
	img := s.Find("img").First()
	if img.Length() == 0 {
		return nil, fmt.Errorf("未找到img元素")
	}

	// 获取图片URL - 优先data-original，备选src
	fm, exists := img.Attr("data-original")
	if !exists {
		fm, exists = img.Attr("src")
		if !exists {
			return nil, fmt.Errorf("未找到图片URL")
		}
	}

	// 查找链接元素
	a := s.Find("a").First()
	if a.Length() == 0 {
		return nil, fmt.Errorf("未找到a元素")
	}

	href, exists := a.Attr("href")
	if !exists {
		return nil, fmt.Errorf("未找到href属性")
	}

	// 查找标题元素
	titleElement := s.Find(".list_n2_title, .meta-title").First()
	if titleElement.Length() == 0 {
		return nil, fmt.Errorf("未找到标题元素")
	}

	title := strings.TrimSpace(titleElement.Text())
	if title == "" {
		return nil, fmt.Errorf("标题为空")
	}

	// 提取xrid
	xrid := extractXRIDFromURL(fm)
	if xrid == 0 {
		xrid = extractXRIDFromURL(href)
	}
	if xrid == 0 {
		return nil, fmt.Errorf("无法提取xrid")
	}

	return &ListItem{
		XRID:  xrid,
		FM:    fm,
		Title: title,
		URL:   href,
	}, nil
}

// extractXRIDFromURL 从URL中提取xrid
func extractXRIDFromURL(url string) int {
	// 匹配数字模式
	re := regexp.MustCompile(`(\d+)`)
	matches := re.FindAllString(url, -1)

	// 查找最可能的xrid（通常是较大的数字）
	for i := len(matches) - 1; i >= 0; i-- {
		if num, err := strconv.Atoi(matches[i]); err == nil && num > 1000 {
			return num
		}
	}

	return 0
}

// extractPageLinks 提取分页链接 - 与xr-rust版本完全一致
func (s *CrawlerService) extractPageLinks(doc *goquery.Document) []PageLink {
	var pageLinks []PageLink

	// 🔗 查找分页链接的选择器 - 与xr-rust版本一致
	doc.Find("div.content div.content_left div.page a").Each(func(i int, sel *goquery.Selection) {
		href, exists := sel.Attr("href")
		if !exists {
			return
		}

		text := strings.TrimSpace(sel.Text())
		if text == "" {
			return
		}

		// 🔢 尝试解析页码
		if pageNum, err := strconv.Atoi(text); err == nil {
			if pageNum > 1 { // 跳过第1页（已经处理过）
				pageLinks = append(pageLinks, PageLink{
					Page: pageNum,
					URL:  href,
				})
			}
		}
	})

	// 📊 按页码排序
	sort.Slice(pageLinks, func(i, j int) bool {
		return pageLinks[i].Page < pageLinks[j].Page
	})

	return pageLinks
}

// SaveOrUpdateItems 保存或更新列表项到数据库 - 使用与Rust版本一致的SQL
func (s *CrawlerService) SaveOrUpdateItems(items []ListItem) (int, int, error) {
	if len(items) == 0 {
		return 0, 0, nil
	}

	log.Printf("💾 开始批量处理 %d 条记录", len(items))

	var insertCount, updateCount int

	// 使用与Rust版本一致的 INSERT ... ON DUPLICATE KEY UPDATE 语句
	for _, item := range items {
		result := s.repo.GetDB().Exec(`
			INSERT INTO xr (xrid, fm, title, url, issave)
			VALUES (?, ?, ?, ?, 0)
			ON DUPLICATE KEY UPDATE
				title = VALUES(title),
				fm = VALUES(fm),
				url = VALUES(url)
		`, item.XRID, item.FM, item.Title, item.URL)

		if result.Error != nil {
			log.Printf("❌ 处理xrid %d 失败: %v", item.XRID, result.Error)
			continue
		}

		// 记录是插入还是更新
		if result.RowsAffected == 1 {
			insertCount++
			log.Printf("📝 新增记录: xrid=%d, title=%s", item.XRID, item.Title)
		} else if result.RowsAffected == 2 {
			updateCount++
			log.Printf("🔄 更新记录: xrid=%d, title=%s", item.XRID, item.Title)
		}
	}

	log.Printf("✅ 批量处理完成: 新增 %d 条，更新 %d 条", insertCount, updateCount)
	return insertCount, updateCount, nil
}

// isValidUTF8 检查字符串是否为有效的UTF-8编码
func isValidUTF8(s string) bool {
	// 简单检查：如果字符串包含大量替换字符，可能是编码问题
	replacementCount := strings.Count(s, "�")
	totalLen := len(s)
	if totalLen > 0 && float64(replacementCount)/float64(totalLen) > 0.01 {
		return false
	}

	// 检查是否包含常见的GBK编码特征
	if strings.Contains(s, "charset=gb") || strings.Contains(s, "charset=GB") {
		return false
	}

	return true
}

// convertToUTF8 尝试将字节数组从GBK转换为UTF-8
func convertToUTF8(data []byte) (string, error) {
	// 首先尝试GBK解码
	reader := transform.NewReader(strings.NewReader(string(data)), simplifiedchinese.GBK.NewDecoder())
	decoded, err := io.ReadAll(reader)
	if err != nil {
		// 如果GBK解码失败，尝试GB18030
		reader = transform.NewReader(strings.NewReader(string(data)), simplifiedchinese.GB18030.NewDecoder())
		decoded, err = io.ReadAll(reader)
		if err != nil {
			return "", fmt.Errorf("编码转换失败: %w", err)
		}
	}

	return string(decoded), nil
}
