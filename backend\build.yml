# 🚀 Backend多平台编译配置文件
# 支持Windows x64、Linux x64、Linux ARM64三个平台的统一编译

# 📋 项目基本信息
project:
  name: "xr-gallery-backend"
  version: "1.0.0"
  description: "XR Gallery Backend Service - 高性能图库后端服务"
  entry_file: "cmd/main.go"
  
# 📁 构建配置
build:
  # 输出目录
  output_dir: "dist"
  
  # 构建选项
  options:
    # Go构建标签
    tags: []
    # 链接器标志
    ldflags: 
      - "-s"  # 去除符号表
      - "-w"  # 去除调试信息
      - "-X main.version={{.Version}}"
      - "-X main.buildTime={{.BuildTime}}"
    # CGO设置
    cgo_enabled: false
    # 构建模式
    build_mode: "default"
    # 优化级别
    optimization: "release"

# 🎯 目标平台配置
targets:
  # Windows x64平台
  - name: "windows-amd64"
    display_name: "Windows x64"
    goos: "windows"
    goarch: "amd64"
    output_name: "main_win_amd64.exe"
    enabled: true
    description: "Windows 64位可执行文件"
    
  # Linux x64平台  
  - name: "linux-amd64"
    display_name: "Linux x64"
    goos: "linux"
    goarch: "amd64"
    output_name: "main_linux_amd64"
    enabled: true
    description: "Linux 64位可执行文件"
    
  # Linux ARM64平台
  - name: "linux-arm64"
    display_name: "Linux ARM64"
    goos: "linux"
    goarch: "arm64"
    output_name: "main_linux_arm64"
    enabled: true
    description: "Linux ARM64可执行文件"

# 🔧 环境变量配置
environment:
  # Go环境变量
  go:
    GO111MODULE: "on"
    GOPROXY: "https://goproxy.cn,direct"
    GOSUMDB: "sum.golang.google.cn"
    
  # 构建环境变量
  build:
    CGO_ENABLED: "0"
    GOFLAGS: "-trimpath"

# 📦 打包配置
package:
  # 是否启用打包
  enabled: true
  
  # 打包格式
  formats:
    - "zip"    # Windows平台使用zip
    - "tar.gz" # Linux平台使用tar.gz
    
  # 包含的额外文件
  include_files:
    - "README.md"
    - "config/*.yaml"
    - "LICENSE"
    
  # 排除的文件模式
  exclude_patterns:
    - "*.log"
    - "*.tmp"
    - ".git*"
    - "node_modules"

# 🧪 测试配置
test:
  # 是否在构建前运行测试
  run_before_build: true
  
  # 测试命令
  commands:
    - "go test ./..."
    - "go vet ./..."
    
  # 测试覆盖率
  coverage:
    enabled: true
    threshold: 80
    output_file: "coverage.out"

# 📊 构建后处理
post_build:
  # 文件大小检查
  size_check:
    enabled: true
    max_size: "50MB"
    
  # 生成校验和
  checksum:
    enabled: true
    algorithms:
      - "sha256"
      - "md5"
      
  # 构建报告
  report:
    enabled: true
    format: "json"
    output_file: "build_report.json"

# 🔍 验证配置
validation:
  # 构建前验证
  pre_build:
    - check_go_version: ">=1.19"
    - check_dependencies: true
    - check_syntax: true
    
  # 构建后验证
  post_build:
    - verify_executables: true
    - run_smoke_tests: true

# 📝 日志配置
logging:
  level: "info"
  format: "text"
  output: "console"
  
  # 日志文件
  file:
    enabled: true
    path: "build.log"
    max_size: "10MB"
    max_backups: 3

# 🚀 部署配置（可选）
deploy:
  # 是否启用自动部署
  enabled: false
  
  # 部署目标
  targets:
    - name: "staging"
      type: "ssh"
      host: "staging.example.com"
      path: "/opt/xr-gallery"
      
    - name: "production"
      type: "docker"
      registry: "registry.example.com"
      image: "xr-gallery/backend"

# 🔄 CI/CD集成
ci:
  # GitHub Actions
  github_actions:
    enabled: true
    workflow_file: ".github/workflows/build.yml"
    
  # Docker构建
  docker:
    enabled: true
    dockerfile: "Dockerfile"
    base_image: "alpine:latest"
    
# 📋 元数据
metadata:
  author: "XR Gallery Team"
  license: "MIT"
  repository: "https://github.com/x1t/xr-web"
  documentation: "https://docs.xr-gallery.com"
  
  # 构建信息
  build_info:
    timestamp: "{{.BuildTime}}"
    commit: "{{.GitCommit}}"
    branch: "{{.GitBranch}}"
    go_version: "{{.GoVersion}}"

# 🎨 自定义脚本
scripts:
  # 构建前脚本
  pre_build:
    - name: "clean"
      command: "rm -rf dist/*"
      description: "清理输出目录"
      
    - name: "deps"
      command: "go mod tidy"
      description: "整理依赖"
      
  # 构建后脚本  
  post_build:
    - name: "permissions"
      command: "chmod +x dist/main_linux_*"
      description: "设置Linux可执行权限"
      platforms: ["linux"]
      
    - name: "compress"
      command: "upx --best dist/main_*"
      description: "压缩可执行文件"
      optional: true

# 🔧 高级选项
advanced:
  # 并行构建
  parallel_build: true
  max_parallel: 3
  
  # 缓存配置
  cache:
    enabled: true
    directory: ".build_cache"
    
  # 增量构建
  incremental: true
  
  # 调试选项
  debug:
    enabled: false
    verbose: false
    keep_temp_files: false
