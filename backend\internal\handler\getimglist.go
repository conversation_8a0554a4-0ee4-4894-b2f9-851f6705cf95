package handler

import (
	"context"
	"fmt"
	"log"
	"xr-gallery/internal/repository"
	"xr-gallery/internal/service"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// 🖼️ GetImgList 内页图片列表爬取处理器 - 与xr-rust版本完全一致的高度优雅实现
//
// 功能特性：
// - 🎯 串行处理，每60秒爬取一个详情页，避免过度请求
// - 🕷️ 智能HTML解析，提取所有图片链接
// - 🔤 完善的中文编码处理，确保title字段无乱码
// - 📊 批量数据库操作，高效存储图片信息
// - 🛡️ 完整的错误处理和状态管理
func GetImgList(ctx context.Context, c *app.RequestContext) {
	log.Printf("🖼️ 开始处理内页图片爬取请求 - 每60秒串行处理模式")

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 获取待处理详情页记录
	record, err := repo.GetPendingDetailRecord()
	if err != nil {
		log.Printf("❌ 获取待处理详情页记录失败: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"error":   fmt.Sprintf("获取待处理详情页记录失败: %v", err),
		})
		return
	}

	if record == nil {
		log.Printf("📭 没有待处理的详情页记录")
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success": true,
			"message": "没有待处理的记录",
		})
		return
	}

	title := record.Title
	if title == "" {
		title = "未知标题"
	}

	if record.URL == "" {
		log.Printf("❌ 记录URL为空: xrid=%d", record.XRID)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(400, utils.H{
			"success": false,
			"error":   "记录URL为空",
			"xrid":    record.XRID,
		})
		return
	}

	log.Printf("🎯 处理记录: xrid=%d, title=%s", record.XRID, title)

	// 智能检查：是否已有足够的图片
	existingCount, successCount, err := repo.CheckExistingImages(record.XRID)
	if err != nil {
		log.Printf("❌ 检查已有图片失败: %v", err)
	} else {
		if existingCount > 0 {
			log.Printf("📊 xrid=%d 已有%d张图片(%d张成功)", record.XRID, existingCount, successCount)

			// 如果成功图片数量>=30，跳过处理
			if successCount >= 30 {
				log.Printf("✅ xrid=%d 已有足够图片，跳过处理", record.XRID)
				if err := repo.UpdateRecordStatus(record.ID, 1); err != nil {
					log.Printf("⚠️  更新完成状态失败: %v", err)
				}
				c.Header("Content-Type", "application/json; charset=utf-8")
				c.JSON(200, utils.H{
					"success":       true,
					"reason":        "sufficient_images",
					"xrid":          record.XRID,
					"existingCount": existingCount,
					"successCount":  successCount,
				})
				return
			}

			// 如果图片数量不足，清理后重新爬取
			if successCount < 30 {
				log.Printf("🔄 xrid=%d 图片数量不足，清理后重新爬取", record.XRID)
				deletedCount, err := repo.CleanExistingImages(record.XRID)
				if err != nil {
					log.Printf("❌ 清理图片失败: %v", err)
				} else {
					log.Printf("🗑️  已清理 xrid=%d 的%d张旧记录", record.XRID, deletedCount)
				}
			}
		}
	}

	// 更新状态为处理中 (issave=3)
	if err := repo.UpdateRecordStatus(record.ID, 3); err != nil {
		log.Printf("❌ 更新状态失败: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"error":   fmt.Sprintf("更新状态失败: %v", err),
		})
		return
	}

	// 创建爬虫服务并爬取详情页图片
	crawlerService := service.NewCrawlerService()

	images, err := crawlerService.GetDetailImages(record.URL)
	if err != nil {
		log.Printf("❌ 爬取详情页失败: xrid=%d, error=%v", record.XRID, err)

		// 爬取失败，重置状态
		if err := repo.UpdateRecordStatus(record.ID, 0); err != nil {
			log.Printf("⚠️  重置状态失败: %v", err)
		}

		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"xrid":    record.XRID,
			"error":   fmt.Sprintf("爬取详情页失败: %v", err),
		})
		return
	}

	imageCount := len(images)
	log.Printf("📸 成功获取%d张图片", imageCount)

	if imageCount == 0 {
		// 没有图片，重置状态
		if err := repo.UpdateRecordStatus(record.ID, 0); err != nil {
			log.Printf("⚠️  重置状态失败: %v", err)
		}

		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success": false,
			"reason":  "no_images_found",
			"xrid":    record.XRID,
			"message": "未找到图片",
		})
		return
	}

	// 保存图片记录到数据库
	savedCount, err := repo.BatchCreateImages(record.XRID, images)
	if err != nil {
		log.Printf("❌ 保存图片记录失败: %v", err)

		// 保存失败，重置状态
		if err := repo.UpdateRecordStatus(record.ID, 0); err != nil {
			log.Printf("⚠️  重置状态失败: %v", err)
		}

		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success":    false,
			"xrid":       record.XRID,
			"error":      fmt.Sprintf("保存图片记录失败: %v", err),
			"imageCount": imageCount,
		})
		return
	}

	// 更新状态为完成 (issave=1)
	if err := repo.UpdateRecordStatus(record.ID, 1); err != nil {
		log.Printf("⚠️  更新完成状态失败: %v", err)
	}

	log.Printf("🎉 xrid=%d 处理完成: 获取%d张图片，保存%d张", record.XRID, imageCount, savedCount)

	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"success":    true,
		"xrid":       record.XRID,
		"title":      title,
		"imageCount": imageCount,
		"savedCount": savedCount,
	})
}
