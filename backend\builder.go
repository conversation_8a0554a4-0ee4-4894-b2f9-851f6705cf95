package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// 🏗️ 构建配置结构体
type BuildConfig struct {
	Project struct {
		Name      string `yaml:"name"`
		Version   string `yaml:"version"`
		EntryFile string `yaml:"entry_file"`
	} `yaml:"project"`

	Build struct {
		OutputDir string `yaml:"output_dir"`
	} `yaml:"build"`

	Targets []struct {
		Name        string `yaml:"name"`
		DisplayName string `yaml:"display_name"`
		GOOS        string `yaml:"goos"`
		GOARCH      string `yaml:"goarch"`
		OutputName  string `yaml:"output_name"`
		Enabled     bool   `yaml:"enabled"`
	} `yaml:"targets"`
}

// 🎯 主函数
func main() {
	fmt.Println("🚀 XR Gallery Backend 多平台构建工具")
	fmt.Println("📋 基于 build.yml 配置文件的统一构建方案")
	fmt.Println()

	// 📖 读取配置文件
	config, err := loadConfig("build.yml")
	if err != nil {
		log.Fatalf("❌ 读取配置文件失败: %v", err)
	}

	fmt.Printf("📦 项目: %s v%s\n", config.Project.Name, config.Project.Version)
	fmt.Println()

	// 📁 创建输出目录
	if err := createOutputDir(config.Build.OutputDir); err != nil {
		log.Fatalf("❌ 创建输出目录失败: %v", err)
	}

	// 🏗️ 构建所有目标平台
	fmt.Println("🏗️ 开始多平台构建...")
	successCount := 0
	totalCount := 0

	for _, target := range config.Targets {
		if !target.Enabled {
			fmt.Printf("⏭️  跳过 %s (已禁用)\n", target.DisplayName)
			continue
		}

		totalCount++
		fmt.Printf("🎯 构建 %s...\n", target.DisplayName)

		if err := buildTarget(config, target); err != nil {
			fmt.Printf("❌ %s 构建失败: %v\n", target.DisplayName, err)
			continue
		}

		fmt.Printf("✅ %s 构建成功\n", target.DisplayName)
		successCount++
	}

	// 📊 构建结果统计
	fmt.Println("\n📊 构建结果统计:")
	fmt.Printf("   ✅ 成功: %d/%d\n", successCount, totalCount)
	if successCount == totalCount {
		fmt.Println("🎉 所有平台构建成功！")
	} else {
		fmt.Printf("⚠️  %d个平台构建失败\n", totalCount-successCount)
	}

	// 📁 显示输出文件
	fmt.Println("\n📁 输出文件:")
	listOutputFiles(config.Build.OutputDir)

	fmt.Println("\n🎉 构建流程完成！")
}

// 📖 读取配置文件
func loadConfig(filename string) (*BuildConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	var config BuildConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析YAML失败: %w", err)
	}

	return &config, nil
}

// 📁 创建输出目录
func createOutputDir(dir string) error {
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}
	fmt.Printf("📁 输出目录: %s\n", dir)
	return nil
}

// 🏗️ 构建目标平台
func buildTarget(config *BuildConfig, target struct {
	Name        string `yaml:"name"`
	DisplayName string `yaml:"display_name"`
	GOOS        string `yaml:"goos"`
	GOARCH      string `yaml:"goarch"`
	OutputName  string `yaml:"output_name"`
	Enabled     bool   `yaml:"enabled"`
}) error {
	// 设置环境变量
	env := os.Environ()
	env = append(env, fmt.Sprintf("GOOS=%s", target.GOOS))
	env = append(env, fmt.Sprintf("GOARCH=%s", target.GOARCH))
	env = append(env, "CGO_ENABLED=0")

	// 构建输出路径
	outputPath := filepath.Join(config.Build.OutputDir, target.OutputName)

	// 构建命令
	buildTime := time.Now().Format("2006-01-02T15:04:05Z07:00")
	ldflags := fmt.Sprintf("-s -w -X main.version=%s -X main.buildTime=%s", 
		config.Project.Version, buildTime)

	args := []string{"build", "-ldflags", ldflags, "-o", outputPath, config.Project.EntryFile}

	// 执行构建
	cmd := exec.Command("go", args...)
	cmd.Env = env
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

// 📁 列出输出文件
func listOutputFiles(dir string) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		fmt.Printf("⚠️  读取目录失败: %v\n", err)
		return
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		info, err := entry.Info()
		if err != nil {
			continue
		}

		fmt.Printf("   📄 %s (%s)\n", entry.Name(), formatFileSize(info.Size()))
	}
}

// 📊 格式化文件大小
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}
