name: 🏗️ Build & Test

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  # 🔧 Backend Go 构建
  backend:
    name: 🐹 Backend (Go)
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 🐹 Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
        
    - name: 📦 Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('backend/go.sum') }}
        
    - name: 📥 Download dependencies
      working-directory: ./backend
      run: go mod download
      
    - name: 🧪 Run tests
      working-directory: ./backend
      run: go test -v ./...
      
    - name: 🏗️ Build
      working-directory: ./backend
      run: go build -v ./cmd/main.go
      
    - name: 🎯 Build with YAML tool
      working-directory: ./backend
      run: go run builder.go

  # 🦀 Rust 构建
  rust:
    name: 🦀 Rust
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 🦀 Setup Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
        
    - name: 📦 Cache Rust dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          xr-rust/target
        key: ${{ runner.os }}-cargo-${{ hashFiles('xr-rust/Cargo.lock') }}
        
    - name: 🧪 Run tests
      working-directory: ./xr-rust
      run: cargo test
      
    - name: 🏗️ Build
      working-directory: ./xr-rust
      run: cargo build --release

  # 🎨 Frontend 构建
  frontend:
    name: 🎨 Frontend (Vue)
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: 📦 Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: latest
        
    - name: 📦 Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.pnpm-store
        key: ${{ runner.os }}-pnpm-${{ hashFiles('frontend/pnpm-lock.yaml') }}
        
    - name: 📥 Install dependencies
      working-directory: ./frontend
      run: pnpm install --frozen-lockfile
      
    - name: 🏗️ Build
      working-directory: ./frontend
      run: pnpm build

  # 📊 构建状态汇总
  build-summary:
    name: 📊 Build Summary
    runs-on: ubuntu-latest
    needs: [backend, rust, frontend]
    if: always()
    
    steps:
    - name: 📊 Check build results
      run: |
        echo "🏗️ Build Summary:"
        echo "Backend (Go): ${{ needs.backend.result }}"
        echo "Rust: ${{ needs.rust.result }}"
        echo "Frontend (Vue): ${{ needs.frontend.result }}"
        
        if [ "${{ needs.backend.result }}" = "success" ] && [ "${{ needs.rust.result }}" = "success" ] && [ "${{ needs.frontend.result }}" = "success" ]; then
          echo "🎉 All builds successful!"
          exit 0
        else
          echo "❌ Some builds failed"
          exit 1
        fi
