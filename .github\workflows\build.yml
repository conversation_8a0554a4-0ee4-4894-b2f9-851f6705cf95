name: 🐹 Backend Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  # 🔧 Backend Go 构建
  backend:
    name: 🐹 Go Backend Build & Test
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐹 Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: 📦 Cache Go modules
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('backend/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: 📥 Download dependencies
      working-directory: ./backend
      run: go mod download

    - name: 🧪 Run tests
      working-directory: ./backend
      run: go test -v ./...

    - name: 🔍 Run go vet
      working-directory: ./backend
      run: go vet ./...

    - name: Build main binary
      working-directory: ./backend
      run: go build -v -o main ./cmd/main.go

    - name: 🎯 Build with YAML tool
      working-directory: ./backend
      run: |
        go mod tidy
        go run builder.go

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: backend-binaries
        path: |
          backend/main
          backend/dist/
        retention-days: 7

    - name: 🎉 Build summary
      run: |
        echo "� Backend build completed successfully!"
        echo "✅ Tests passed"
        echo "✅ Go vet checks passed"
        echo "✅ Main binary built"
        echo "✅ Multi-platform binaries built"
