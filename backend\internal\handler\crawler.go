package handler

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"xr-gallery/internal/service"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// 📋 GetList 获取列表页数据并保存到数据库 - 与xr-rust版本完全一致的高度优雅实现
//
// 功能特性：
// - 🎯 串行处理，每10分钟获取一页列表数据，避免过度请求
// - 🕷️ 智能HTML解析，提取所有列表项信息
// - 🔤 完善的中文编码处理，确保title字段无乱码
// - 📊 批量数据库操作，使用INSERT...ON DUPLICATE KEY UPDATE
// - 🛡️ 完整的错误处理和状态管理
func GetList(ctx context.Context, c *app.RequestContext) {
	log.Printf("📋 开始处理getlist请求 - 每10分钟串行处理模式")

	// 获取查询参数
	pageStr := c.DefaultQuery("page", "1")

	// 参数验证和转换
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	// 确保页码至少为1
	if page < 1 {
		page = 1
	}

	log.Printf("🎯 处理第%d页列表数据", page)

	// 创建爬虫服务实例
	crawlerService := service.NewCrawlerService()

	// 爬取列表页数据
	items, err := crawlerService.GetListPage(page)
	if err != nil {
		log.Printf("❌ 爬取第%d页失败: %v", page, err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"page":    page,
			"error":   fmt.Sprintf("爬取第%d页失败: %v", page, err),
		})
		return
	}

	if len(items) == 0 {
		log.Printf("⚠️  第%d页未获取到任何数据", page)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success":      true,
			"page":         page,
			"count":        0,
			"data":         []interface{}{},
			"saved_count":  0,
			"insert_count": 0,
			"update_count": 0,
		})
		return
	}

	// 保存或更新数据到数据库
	insertCount, updateCount, err := crawlerService.SaveOrUpdateItems(items)
	if err != nil {
		log.Printf("❌ 保存数据失败: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success":        true,
			"page":           page,
			"count":          len(items),
			"data":           items,
			"saved_count":    0,
			"insert_count":   0,
			"update_count":   0,
			"database_error": fmt.Sprintf("数据库保存失败: %v", err),
		})
		return
	}

	log.Printf("✅ 第%d页处理完成: 爬取%d条, 插入%d条, 更新%d条", page, len(items), insertCount, updateCount)

	// 返回成功响应 - 与Rust版本格式一致
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"success":      true,
		"page":         page,
		"count":        len(items),
		"data":         items,
		"saved_count":  insertCount + updateCount,
		"insert_count": insertCount,
		"update_count": updateCount,
		"performance": utils.H{
			"go_powered":     true,
			"memory_safe":    true,
			"fast_execution": true,
			"concurrent":     true,
		},
	})
}
