package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// 🏗️ 构建配置结构体
type BuildConfig struct {
	Project struct {
		Name        string `yaml:"name"`
		Version     string `yaml:"version"`
		Description string `yaml:"description"`
		EntryFile   string `yaml:"entry_file"`
	} `yaml:"project"`

	Build struct {
		OutputDir string `yaml:"output_dir"`
		Options   struct {
			Tags         []string `yaml:"tags"`
			LdFlags      []string `yaml:"ldflags"`
			CgoEnabled   bool     `yaml:"cgo_enabled"`
			BuildMode    string   `yaml:"build_mode"`
			Optimization string   `yaml:"optimization"`
		} `yaml:"options"`
	} `yaml:"build"`

	Targets []struct {
		Name        string `yaml:"name"`
		DisplayName string `yaml:"display_name"`
		GOOS        string `yaml:"goos"`
		GOARCH      string `yaml:"goarch"`
		OutputName  string `yaml:"output_name"`
		Enabled     bool   `yaml:"enabled"`
		Description string `yaml:"description"`
	} `yaml:"targets"`

	Environment struct {
		Go struct {
			GO111MODULE string `yaml:"GO111MODULE"`
			GOPROXY     string `yaml:"GOPROXY"`
			GOSUMDB     string `yaml:"GOSUMDB"`
		} `yaml:"go"`
		Build struct {
			CGOEnabled string `yaml:"CGO_ENABLED"`
			GOFLAGS    string `yaml:"GOFLAGS"`
		} `yaml:"build"`
	} `yaml:"environment"`

	Test struct {
		RunBeforeBuild bool     `yaml:"run_before_build"`
		Commands       []string `yaml:"commands"`
	} `yaml:"test"`

	Scripts struct {
		PreBuild []struct {
			Name        string   `yaml:"name"`
			Command     string   `yaml:"command"`
			Description string   `yaml:"description"`
			Platforms   []string `yaml:"platforms"`
		} `yaml:"pre_build"`
		PostBuild []struct {
			Name        string   `yaml:"name"`
			Command     string   `yaml:"command"`
			Description string   `yaml:"description"`
			Platforms   []string `yaml:"platforms"`
			Optional    bool     `yaml:"optional"`
		} `yaml:"post_build"`
	} `yaml:"scripts"`
}

// 🎯 主函数
func main() {
	fmt.Println("🚀 XR Gallery Backend 多平台构建工具")
	fmt.Println("📋 基于 build.yml 配置文件的统一构建方案")
	fmt.Println()

	// 📖 读取配置文件
	config, err := loadConfig("build.yml")
	if err != nil {
		log.Fatalf("❌ 读取配置文件失败: %v", err)
	}

	fmt.Printf("📦 项目: %s v%s\n", config.Project.Name, config.Project.Version)
	fmt.Printf("📝 描述: %s\n", config.Project.Description)
	fmt.Println()

	// 🧪 运行测试（如果启用）
	if config.Test.RunBeforeBuild {
		fmt.Println("🧪 运行构建前测试...")
		if err := runTests(config); err != nil {
			log.Fatalf("❌ 测试失败: %v", err)
		}
		fmt.Println("✅ 测试通过")
		fmt.Println()
	}

	// 🔧 运行构建前脚本
	fmt.Println("🔧 执行构建前脚本...")
	if err := runPreBuildScripts(config); err != nil {
		log.Fatalf("❌ 构建前脚本执行失败: %v", err)
	}
	fmt.Println()

	// 📁 创建输出目录
	if err := createOutputDir(config.Build.OutputDir); err != nil {
		log.Fatalf("❌ 创建输出目录失败: %v", err)
	}

	// 🏗️ 构建所有目标平台
	fmt.Println("🏗️ 开始多平台构建...")
	successCount := 0
	totalCount := 0

	for _, target := range config.Targets {
		if !target.Enabled {
			fmt.Printf("⏭️  跳过 %s (已禁用)\n", target.DisplayName)
			continue
		}

		totalCount++
		fmt.Printf("🎯 构建 %s...\n", target.DisplayName)

		if err := buildTarget(config, target); err != nil {
			fmt.Printf("❌ %s 构建失败: %v\n", target.DisplayName, err)
			continue
		}

		fmt.Printf("✅ %s 构建成功\n", target.DisplayName)
		successCount++
	}

	fmt.Println()

	// 🔧 运行构建后脚本
	fmt.Println("🔧 执行构建后脚本...")
	if err := runPostBuildScripts(config); err != nil {
		fmt.Printf("⚠️  构建后脚本执行失败: %v\n", err)
	}

	// 📊 构建结果统计
	fmt.Println("📊 构建结果统计:")
	fmt.Printf("   ✅ 成功: %d/%d\n", successCount, totalCount)
	if successCount == totalCount {
		fmt.Println("🎉 所有平台构建成功！")
	} else {
		fmt.Printf("⚠️  %d个平台构建失败\n", totalCount-successCount)
	}

	// 📁 显示输出文件
	fmt.Println("\n📁 输出文件:")
	if err := listOutputFiles(config.Build.OutputDir); err != nil {
		fmt.Printf("⚠️  列出输出文件失败: %v\n", err)
	}

	fmt.Println("\n🎉 构建流程完成！")
}

// 📖 读取配置文件
func loadConfig(filename string) (*BuildConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	var config BuildConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析YAML失败: %w", err)
	}

	return &config, nil
}

// 🧪 运行测试
func runTests(config *BuildConfig) error {
	for _, cmd := range config.Test.Commands {
		fmt.Printf("   🔍 执行: %s\n", cmd)
		if err := runCommand(cmd); err != nil {
			return fmt.Errorf("测试命令失败 '%s': %w", cmd, err)
		}
	}
	return nil
}

// 🔧 运行构建前脚本
func runPreBuildScripts(config *BuildConfig) error {
	for _, script := range config.Scripts.PreBuild {
		if !shouldRunOnPlatform(script.Platforms) {
			fmt.Printf("   ⏭️  跳过 %s (平台不匹配)\n", script.Name)
			continue
		}

		fmt.Printf("   🔧 %s: %s\n", script.Name, script.Description)
		if err := runCommand(script.Command); err != nil {
			return fmt.Errorf("脚本 '%s' 执行失败: %w", script.Name, err)
		}
	}
	return nil
}

// 🔧 运行构建后脚本
func runPostBuildScripts(config *BuildConfig) error {
	for _, script := range config.Scripts.PostBuild {
		if !shouldRunOnPlatform(script.Platforms) {
			fmt.Printf("   ⏭️  跳过 %s (平台不匹配)\n", script.Name)
			continue
		}

		fmt.Printf("   🔧 %s: %s\n", script.Name, script.Description)
		if err := runCommand(script.Command); err != nil {
			if script.Optional {
				fmt.Printf("   ⚠️  可选脚本 '%s' 执行失败: %v\n", script.Name, err)
				continue
			}
			return fmt.Errorf("脚本 '%s' 执行失败: %w", script.Name, err)
		}
	}
	return nil
}

// 📁 创建输出目录
func createOutputDir(dir string) error {
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}
	fmt.Printf("📁 输出目录: %s\n", dir)
	return nil
}

// 🏗️ 构建目标平台
func buildTarget(config *BuildConfig, target struct {
	Name        string `yaml:"name"`
	DisplayName string `yaml:"display_name"`
	GOOS        string `yaml:"goos"`
	GOARCH      string `yaml:"goarch"`
	OutputName  string `yaml:"output_name"`
	Enabled     bool   `yaml:"enabled"`
	Description string `yaml:"description"`
}) error {
	// 设置环境变量
	env := os.Environ()
	env = append(env, fmt.Sprintf("GOOS=%s", target.GOOS))
	env = append(env, fmt.Sprintf("GOARCH=%s", target.GOARCH))
	env = append(env, fmt.Sprintf("CGO_ENABLED=%s", config.Environment.Build.CGOEnabled))

	// 构建输出路径
	outputPath := filepath.Join(config.Build.OutputDir, target.OutputName)

	// 构建命令
	args := []string{"build"}

	// 添加ldflags
	if len(config.Build.Options.LdFlags) > 0 {
		ldflags := strings.Join(config.Build.Options.LdFlags, " ")
		ldflags = strings.ReplaceAll(ldflags, "{{.Version}}", config.Project.Version)
		ldflags = strings.ReplaceAll(ldflags, "{{.BuildTime}}", time.Now().Format("2006-01-02T15:04:05Z07:00"))
		args = append(args, "-ldflags", ldflags)
	}

	// 添加输出路径和入口文件
	args = append(args, "-o", outputPath, config.Project.EntryFile)

	// 执行构建
	cmd := exec.Command("go", args...)
	cmd.Env = env
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

// 🔍 检查是否应该在当前平台运行
func shouldRunOnPlatform(platforms []string) bool {
	if len(platforms) == 0 {
		return true // 没有指定平台限制，在所有平台运行
	}

	currentOS := runtime.GOOS
	for _, platform := range platforms {
		if platform == currentOS {
			return true
		}
	}
	return false
}

// 🔧 运行命令
func runCommand(command string) error {
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return fmt.Errorf("空命令")
	}

	cmd := exec.Command(parts[0], parts[1:]...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

// 📁 列出输出文件
func listOutputFiles(dir string) error {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return err
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		info, err := entry.Info()
		if err != nil {
			continue
		}

		fmt.Printf("   📄 %s (%s)\n", entry.Name(), formatFileSize(info.Size()))
	}

	return nil
}

// 📊 格式化文件大小
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}
