import { createRouter, createWebHistory } from 'vue-router'
import { getDetailRouteName } from '@/utils/device'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: 'XR Gallery - 首页'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/detail/:xrid',
    name: 'Detail',
    component: () => import('@/views/Detail.vue'),
    meta: {
      title: '图片详情'
    },
    props: true
  },
  {
    path: '/mobile/detail/:xrid',
    name: 'DetailMobile',
    component: () => import('@/views/DetailMobile.vue'),
    meta: {
      title: '图片详情 - 移动端'
    },
    props: true
  },
  {
    path: '/desktop/detail/:xrid',
    name: 'DetailDesktop',
    component: () => import('@/views/DetailDesktop.vue'),
    meta: {
      title: '图片详情 - 桌面端'
    },
    props: true
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 认证检查和页面标题设置
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 定义不需要认证的路由
  const publicRoutes = ['Login']

  // 检查是否需要认证
  const requiresAuth = !publicRoutes.includes(to.name)

  // 认证检查逻辑
  if (requiresAuth && !authStore.isAuthenticated) {
    // 未登录且访问受保护路由，重定向到登录页
    console.log('未登录用户访问受保护路由，重定向到登录页')
    next({ name: 'Login' })
    return
  }

  if (to.name === 'Login' && authStore.isAuthenticated) {
    // 已登录用户访问登录页，重定向到首页
    console.log('已登录用户访问登录页，重定向到首页')
    next({ name: 'Home' })
    return
  }

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  next()
})

export default router
